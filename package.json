{"name": "fish-eat-fish-multiplayer", "version": "1.0.0", "description": "Multiplayer version of Fish Eat Fish game", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "socket.io-client": "^4.8.1", "ws": "^8.18.1"}, "devDependencies": {"@playwright/test": "^1.52.0", "nodemon": "^3.0.1"}}