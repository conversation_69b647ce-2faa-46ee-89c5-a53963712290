import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get directory path for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create the Express app and HTTP server
const app = express();
const server = createServer(app);
const io = new Server(server);

// Serve static files from the current directory
app.use(express.static(__dirname));

// Game state
const gameState = {
    players: {},
    foods: [],
    enemies: [],
    worldWidth: 3840,  // Default, will be updated based on client window size
    worldHeight: 2160, // Default, will be updated based on client window size
    maxFoodItems: 100,
    connectedPlayers: 0,  // Track number of connected players
    activePlayers: 0,     // Track number of players who have joined the game
    foodIdCounter: 0      // Counter for unique food IDs
};

// Food generation with improved ID generation
function generateFood() {
    if (gameState.foods.length >= gameState.maxFoodItems) return;

    // Create a new food item with unique ID and random position
    const food = {
        id: `food_${Date.now()}_${++gameState.foodIdCounter}`,
        x: Math.random() * gameState.worldWidth,
        y: Math.random() * gameState.worldHeight,
        radius: 5,
        value: 1,
        color: getRandomFoodColor(),
        createdAt: Date.now()
    };

    gameState.foods.push(food);
    return food;
}

// Clean up old food items (prevent memory leaks)
function cleanupOldFood() {
    const now = Date.now();
    const maxFoodAge = 300000; // 5 minutes

    const initialLength = gameState.foods.length;
    gameState.foods = gameState.foods.filter(food => {
        return (now - food.createdAt) < maxFoodAge;
    });

    const removedCount = initialLength - gameState.foods.length;
    if (removedCount > 0) {
        console.log(`Cleaned up ${removedCount} old food items`);
    }
}

function getRandomFoodColor() {
    const colors = ['#AAFFAA', '#AAAAFF', '#FFAAAA', '#FFFFAA', '#FFAAFF', '#AAFFFF'];
    return colors[Math.floor(Math.random() * colors.length)];
}

// Validate player data to prevent invalid states
function validatePlayerData(playerData) {
    if (!playerData) return false;

    // Check required fields
    if (typeof playerData.x !== 'number' || typeof playerData.y !== 'number') return false;
    if (typeof playerData.radius !== 'number' || playerData.radius <= 0) return false;
    if (typeof playerData.angle !== 'number') return false;
    if (typeof playerData.score !== 'number' || playerData.score < 0) return false;
    if (typeof playerData.sizeLevel !== 'number' || playerData.sizeLevel < 1) return false;

    // Check bounds
    if (playerData.x < 0 || playerData.x > gameState.worldWidth) return false;
    if (playerData.y < 0 || playerData.y > gameState.worldHeight) return false;
    if (playerData.radius > 200) return false; // Prevent extremely large fish
    if (playerData.sizeLevel > 50) return false; // Reasonable size limit

    return true;
}

// Safe error handling wrapper
function safeEventHandler(handler) {
    return (...args) => {
        try {
            return handler(...args);
        } catch (error) {
            console.error('Error in event handler:', error);
        }
    };
}

// Generate initial food
for (let i = 0; i < 50; i++) {
    generateFood();
}

// Food spawning interval - optimized for player count
let foodSpawnInterval;

function startFoodSpawning() {
    if (foodSpawnInterval) {
        clearInterval(foodSpawnInterval);
    }

    // Adjust spawn rate based on number of active players (who have joined the game)
    let spawnRate;
    if (gameState.activePlayers === 0) {
        return; // No spawning if no active players
    } else if (gameState.activePlayers === 1) {
        spawnRate = 2000; // Slower spawning for single player (every 2 seconds)
    } else {
        spawnRate = 500; // Normal spawning for multiplayer (every 500ms)
    }

    foodSpawnInterval = setInterval(() => {
        const newFood = generateFood();
        if (newFood && gameState.activePlayers > 0) {
            io.emit('foodSpawned', newFood);
        }

        // Periodically clean up old food
        if (Math.random() < 0.1) { // 10% chance each spawn
            cleanupOldFood();
        }
    }, spawnRate);
}

// Start initial food spawning
startFoodSpawning();

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log('A user connected:', socket.id);

    // Increment connected players count
    gameState.connectedPlayers++;
    console.log(`Connected players: ${gameState.connectedPlayers}`);

    // Restart food spawning with new player count
    startFoodSpawning();

    // Send current game state to the new player
    socket.emit('gameState', gameState);

    // Handle player joining
    socket.on('playerJoin', safeEventHandler((playerData) => {
        console.log('Player joined:', playerData.name);

        // Validate player data
        if (!playerData || !playerData.name || typeof playerData.name !== 'string') {
            console.error('Invalid player data received');
            socket.emit('error', 'Invalid player data');
            return;
        }

        // Sanitize player name
        const sanitizedName = playerData.name.substring(0, 15).replace(/[<>"'&]/g, '');

        // Create player in game state
        gameState.players[socket.id] = {
            id: socket.id,
            name: sanitizedName,
            x: Math.max(0, Math.min(playerData.x || 100, gameState.worldWidth)),
            y: Math.max(0, Math.min(playerData.y || 100, gameState.worldHeight)),
            radius: Math.max(10, Math.min(playerData.radius || 15, 50)),
            color: playerData.color || '#FF6633',
            eyeColor: playerData.eyeColor || 'white',
            pupilColor: playerData.pupilColor || 'black',
            angle: playerData.angle || 0,
            score: 0,
            sizeLevel: 1,
            isAlive: true,
            joinedAt: Date.now()
        };

        // Increment active players count
        gameState.activePlayers++;
        console.log(`Active players: ${gameState.activePlayers}`);

        // Update world dimensions if needed
        if (playerData.worldWidth && playerData.worldHeight) {
            gameState.worldWidth = Math.max(gameState.worldWidth, playerData.worldWidth);
            gameState.worldHeight = Math.max(gameState.worldHeight, playerData.worldHeight);
        }

        // Restart food spawning with new player count
        startFoodSpawning();

        // Broadcast new player to all other players (only if there are other players)
        if (gameState.activePlayers > 1) {
            socket.broadcast.emit('playerJoined', gameState.players[socket.id]);
        }
    }));

    // Handle player movement
    socket.on('playerUpdate', safeEventHandler((playerData) => {
        const player = gameState.players[socket.id];
        if (player && validatePlayerData(playerData)) {
            // Update player data with validation
            player.x = Math.max(0, Math.min(playerData.x, gameState.worldWidth));
            player.y = Math.max(0, Math.min(playerData.y, gameState.worldHeight));
            player.radius = Math.max(5, Math.min(playerData.radius, 200));
            player.angle = playerData.angle;
            player.score = Math.max(0, playerData.score);
            player.sizeLevel = Math.max(1, Math.min(playerData.sizeLevel, 50));
            player.isAlive = Boolean(playerData.isAlive);
            player.lastUpdate = Date.now();

            // Broadcast player update to all other players (only if there are other players)
            if (gameState.activePlayers > 1) {
                socket.broadcast.emit('playerMoved', player);
            }
        } else if (!player) {
            console.warn(`Player update received for non-existent player: ${socket.id}`);
        } else {
            console.warn(`Invalid player data received from ${socket.id}`);
        }
    }));

    // Handle food eaten
    socket.on('foodEaten', safeEventHandler((foodId) => {
        // Validate food ID
        if (!foodId || typeof foodId !== 'string') {
            console.warn(`Invalid food ID received from ${socket.id}`);
            return;
        }

        // Remove food from game state
        const foodIndex = gameState.foods.findIndex(food => food.id === foodId);
        if (foodIndex !== -1) {
            const removedFood = gameState.foods.splice(foodIndex, 1)[0];
            console.log(`Food ${foodId} eaten by ${socket.id}`);

            // Broadcast food eaten to all players
            io.emit('foodRemoved', foodId);
        } else {
            console.warn(`Food ${foodId} not found when trying to remove`);
        }
    }));

    // Handle player eating another player
    socket.on('playerEaten', safeEventHandler((eatenPlayerId) => {
        // Validate eaten player ID
        if (!eatenPlayerId || typeof eatenPlayerId !== 'string') {
            console.warn(`Invalid eaten player ID received from ${socket.id}`);
            return;
        }

        const eatenPlayer = gameState.players[eatenPlayerId];
        const eaterPlayer = gameState.players[socket.id];

        if (eatenPlayer && eaterPlayer && eatenPlayer.isAlive) {
            // Validate that the eater can actually eat the eaten player
            if (eaterPlayer.radius > eatenPlayer.radius * 0.99) {
                eatenPlayer.isAlive = false;
                eatenPlayer.deathTime = Date.now();

                console.log(`Player ${eatenPlayerId} eaten by ${socket.id}`);

                // Broadcast player eaten to all players
                io.emit('playerDied', eatenPlayerId);
            } else {
                console.warn(`Invalid player eating attempt: ${socket.id} cannot eat ${eatenPlayerId}`);
            }
        } else if (!eatenPlayer) {
            console.warn(`Eaten player ${eatenPlayerId} not found`);
        } else if (!eatenPlayer.isAlive) {
            console.warn(`Player ${eatenPlayerId} is already dead`);
        }
    }));

    // Handle player respawn
    socket.on('playerRespawn', safeEventHandler((playerData) => {
        const player = gameState.players[socket.id];
        if (player && validatePlayerData(playerData)) {
            // Reset player to initial state with validation
            player.x = Math.max(0, Math.min(playerData.x || 100, gameState.worldWidth));
            player.y = Math.max(0, Math.min(playerData.y || 100, gameState.worldHeight));
            player.radius = Math.max(10, Math.min(playerData.radius || 15, 50));
            player.score = Math.max(0, playerData.score || 0);
            player.sizeLevel = Math.max(1, Math.min(playerData.sizeLevel || 1, 50));
            player.isAlive = true;
            player.respawnTime = Date.now();

            // Clear death-related properties
            delete player.deathTime;

            console.log(`Player ${socket.id} respawned`);

            // Broadcast player respawn to all players (only if there are other players)
            if (gameState.activePlayers > 1) {
                io.emit('playerRespawned', player);
            }
        } else if (!player) {
            console.warn(`Respawn attempted for non-existent player: ${socket.id}`);
        } else {
            console.warn(`Invalid respawn data received from ${socket.id}`);
        }
    }));

    // Handle disconnection
    socket.on('disconnect', safeEventHandler(() => {
        console.log('User disconnected:', socket.id);

        // Decrement connected players count
        gameState.connectedPlayers = Math.max(0, gameState.connectedPlayers - 1);
        console.log(`Connected players: ${gameState.connectedPlayers}`);

        // Remove player from game state and decrement active players
        if (gameState.players[socket.id]) {
            const playerName = gameState.players[socket.id].name;
            delete gameState.players[socket.id];

            // Decrement active players count
            gameState.activePlayers = Math.max(0, gameState.activePlayers - 1);
            console.log(`Active players: ${gameState.activePlayers}`);

            // Broadcast player left to all players (only if there are other players)
            if (gameState.activePlayers > 0) {
                io.emit('playerLeft', socket.id);
            }
            console.log('Player left:', playerName);
        }

        // Restart food spawning with new player count
        startFoodSpawning();
    }));
});

// Periodic cleanup and maintenance
setInterval(() => {
    const now = Date.now();

    // Clean up inactive players (no updates for 30 seconds)
    const inactivePlayers = [];
    for (const [playerId, player] of Object.entries(gameState.players)) {
        if (player.lastUpdate && (now - player.lastUpdate) > 30000) {
            inactivePlayers.push(playerId);
        }
    }

    // Remove inactive players
    for (const playerId of inactivePlayers) {
        console.log(`Removing inactive player: ${playerId}`);
        delete gameState.players[playerId];
        gameState.activePlayers = Math.max(0, gameState.activePlayers - 1);
        io.emit('playerLeft', playerId);
    }

    // Clean up old food
    cleanupOldFood();

    // Log server stats
    console.log(`Server stats - Connected: ${gameState.connectedPlayers}, Active: ${gameState.activePlayers}, Food: ${gameState.foods.length}`);
}, 60000); // Run every minute

// Start the server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
